
class Symbol:
    """
    ``Contract(**kwargs)`` can create any contract using keyword
    arguments. To simplify working with contracts, there are also more
    specialized contracts that take optional positional arguments.
    Some examples::

        Contract(conId=270639)
        Stock('AMD', 'SMART', 'USD')
        Stock('INTC', 'SMART', 'USD', primaryExchange='NASDAQ')
        Forex('EURUSD')
        CFD('IBUS30')
        Future('ES', '20180921', 'GLOBEX')
        Option('SPY', '20170721', 240, 'C', 'SMART')
        Bond(secIdType='ISIN', secId='US03076KAA60')
        Crypto('BTC', 'PAXOS', 'USD')

    Args:
        symbol_id (str): Unique identifier for the symbol.
        conId (int): The unique IB contract identifier.
        symbol (str): The contract (or its underlying) symbol.
        secType (str): The security type:

            * 'STK' = Stock (or ETF)
            * 'OPT' = Option
            * 'FUT' = Future
            * 'IND' = Index
            * 'FOP' = Futures option
            * 'CASH' = Forex pair
            * 'CFD' = CFD
            * 'BAG' = Combo
            * 'WAR' = Warrant
            * 'BOND' = Bond
            * 'CMDTY' = Commodity
            * 'NEWS' = News
            * 'FUND' = Mutual fund
            * 'CRYPTO' = Crypto currency
            * 'EVENT' = Bet on an event
        lastTradeDateOrContractMonth (str): The contract's last trading
            day or contract month (for Options and Futures).
            Strings with format YYYYMM will be interpreted as the
            Contract Month whereas YYYYMMDD will be interpreted as
            Last Trading Day.
        strike (float): The option's strike price.
        right (str): Put or Call.
            Valid values are 'P', 'PUT', 'C', 'CALL', or '' for non-options.
        multiplier (str): The instrument's multiplier (i.e. options, futures).
        exchange (str): The destination exchange.
        currency (str): The underlying's currency.
        localSymbol (str): The contract's symbol within its primary exchange.
            For options, this will be the OCC symbol.
        primaryExchange (str): The contract's primary exchange.
            For smart routed contracts, used to define contract in case
            of ambiguity. Should be defined as native exchange of contract,
            e.g. ISLAND for MSFT. For exchanges which contain a period in name,
            will only be part of exchange name prior to period, i.e. ENEXT
            for ENEXT.BE.
        tradingClass (str): The trading class name for this contract.
            Available in TWS contract description window as well.
            For example, GBL Dec '13 future's trading class is "FGBL".
        includeExpired (bool): If set to true, contract details requests
            and historical data queries can be performed pertaining to
            expired futures contracts. Expired options or other instrument
            types are not available.
        secIdType (str): Security identifier type. Examples for Apple:

                * secIdType='ISIN', secId='US0378331005'
                * secIdType='CUSIP', secId='037833100'
        secId (str): Security identifier.
        description (str): The description for this symbol.
        issuerId (str): The issuer's ID.
        contractDetails (dict): Dictionary containing contract details.
    """
    symbol_id: str = "" # Unique identifier for the symbol
    secType: str = "" # Security type (STK, FUT, OPT, etc.)
    conId: int = 0 # The unique IB contract identifier
    symbol: str = "" # The contract (or its underlying) symbol
    lastTradeDateOrContractMonth: str = "" # YYYYMM or YYYYMMDD format
    strike: float = 0.0 # Strike price for options
    right: str = "" # Put or call (only for options)
    multiplier: str = "" # Contract multiplier
    exchange: str = "" # Destination exchange
    primaryExchange: str = "" # Primary exchange
    currency: str = "" # Underlying currency
    localSymbol: str = "" # Local symbol of the underlying asset
    tradingClass: str = "" # Trading class name
    includeExpired: bool = False # Include expired contracts
    secIdType: str = "" # Security identifier type
    secId: str = "" # Security identifier
    description: str = "" # Description for this symbol
    issuerId: str = "" # The issuer's ID
    contractDetails: dict = {} # Dictionary containing contract details


    def __init__(self, **kwargs):
        """Initialize a new Symbol instance."""

        # Iterate over the annotations and set default values if they exist
        for attr_name, attr_type in self.__annotations__.items():
            if hasattr(Symbol, attr_name):
                # Only set the default if a default value is defined at the class level.
                setattr(self, attr_name, getattr(Symbol, attr_name))

        # Overwrite with any provided values
        class_attribute_names = self.__annotations__.keys()
        for key, value in kwargs.items():
            if key in class_attribute_names:
                setattr(self, key, value)

        # Generate symbol_id if not provided
        if self.symbol_id == "":
            self.symbol_id = self._generate_symbol_id()


    def __repr__(self):
        """Return a string representation of the symbol."""
        return self._generate_symbol_id()


    def __eq__(self, other):
        """Check if two symbols are equal. They are equal when all attributes are equal."""
        if not isinstance(other, Symbol):
            return False

        return (self.symbol_id == other.symbol_id and
                self.secType == other.secType and
                self.conId == other.conId and
                self.symbol == other.symbol and
                self.lastTradeDateOrContractMonth == other.lastTradeDateOrContractMonth and
                self.strike == other.strike and
                self.right == other.right and
                self.multiplier == other.multiplier and
                self.exchange == other.exchange and
                self.primaryExchange == other.primaryExchange and
                self.currency == other.currency and
                self.localSymbol == other.localSymbol and
                self.tradingClass == other.tradingClass and
                self.includeExpired == other.includeExpired and
                self.secIdType == other.secIdType and
                self.secId == other.secId and
                self.description == other.description and
                self.issuerId == other.issuerId)
     
        
    def _generate_symbol_id(self):
        """Generate a unique symbol_id for the symbol."""
        if self.secType == "CASH":
            return f"FOREX[{self.symbol}{self.currency}]"
        elif self.secType == "STK":
            return f"STOCK[{self.symbol},{self.exchange},{self.currency}]"
        elif self.secType == "FUT":
            return f"FUTURE[{self.symbol},{self.lastTradeDateOrContractMonth},{self.exchange},{self.localSymbol},{self.multiplier},{self.currency}]"
        elif self.secType == "CONTFUT":
            return f"CONT_FUTURE[{self.symbol},{self.exchange},{self.localSymbol},{self.multiplier},{self.currency}]"
        elif self.secType == "OPT":
            return f"OPTION[{self.symbol},{self.lastTradeDateOrContractMonth},{self.strike},{self.right},{self.exchange},{self.multiplier},{self.currency}]"
        elif self.secType == "FOP":
            return f"FUTURES_OPTION[{self.symbol},{self.lastTradeDateOrContractMonth},{self.strike},{self.right},{self.exchange},{self.multiplier},{self.currency}]"
        elif self.secType == "IND":
            return f"INDEX[{self.symbol},{self.exchange},{self.currency}]"
        elif self.secType == "CFD":
            return f"CFD[{self.symbol},{self.exchange},{self.currency}]"
        elif self.secType == "CMDTY":
            return f"COMMODITY[{self.symbol},{self.exchange},{self.currency}]"
        elif self.secType == "BOND":
            return f"BOND[{self.symbol},{self.exchange},{self.currency},{self.secIdType},{self.secId}]"
        elif self.secType == "FUND":
            return f"MUTUAL_FUND[{self.symbol},{self.exchange},{self.currency}]"
        elif self.secType == "WAR":
            return f"WARRANT[{self.symbol},{self.exchange},{self.currency}]"
        elif self.secType == "CRYPTO":
            return f"CRYPTO[{self.symbol},{self.exchange},{self.currency}]"
        else:
            raise ValueError(f"Unsupported security type: {self.secType}")


    def to_dict(self):
        return self.__dict__


class Forex(Symbol):
    def __init__(
        self, 
        pair: str,
        **kwargs):
        """
        Foreign exchange currency pair.

        Args:
            pair: Shortcut for specifying symbol and currency, like 'EURUSD'.
            exchange: Destination exchange.
            symbol: Base currency.
            currency: Quote currency.
        """
        assert len(pair) == 6
    
        super().__init__(
            symbol_id = f"FOREX[{pair}]",
            secType="CASH", 
            symbol=pair[:3],  
            currency=pair[3:], 
            **kwargs)

        
class Stock(Symbol):
    def __init__(
        self, 
        symbol: str, 
        exchange: str, 
        currency: str, 
        **kwargs):
        """
        Stock contract.

        Args:
            symbol: Symbol name.
            exchange: Destination exchange.
            currency: Underlying currency.
        """
        super().__init__(
            symbol_id = f"STOCK[{symbol},{exchange},{currency}]",
            secType="STK",
            symbol=symbol,
            exchange=exchange,
            currency=currency,
            **kwargs)


class Future(Symbol):
    def __init__(
        self,
        symbol: str,
        lastTradeDateOrContractMonth: str,
        exchange: str,
        localSymbol: str,
        multiplier: str,
        currency: str,
        **kwargs):
        """
        Future contract.

        Args:
            symbol: Symbol name.
            lastTradeDateOrContractMonth: The option's last trading day
                or contract month.

                * YYYYMM format: To specify last month
                * YYYYMMDD format: To specify last trading day
            exchange: Destination exchange.
            localSymbol: The contract's symbol within its primary exchange.
            multiplier: The contract multiplier.
            currency: Underlying currency.
        """
        super().__init__(
            self,
            symbol_id = f"FUTURE[{symbol},{lastTradeDateOrContractMonth},{exchange},{localSymbol},{multiplier},{currency}]",
            secType="FUT",
            symbol=symbol,
            lastTradeDateOrContractMonth=lastTradeDateOrContractMonth,
            exchange=exchange,
            localSymbol=localSymbol,
            multiplier=multiplier,
            currency=currency,
            **kwargs)


class ContFuture(Symbol):
    def __init__(
        self,
        symbol: str,
        exchange: str,
        localSymbol: str,
        multiplier: str,
        currency: str,
        **kwargs):
        """
        Continuous future contract.

        Args:
            symbol: Symbol name.
            exchange: Destination exchange.
            localSymbol: The contract's symbol within its primary exchange.
            multiplier: The contract multiplier.
            currency: Underlying currency.
        """
        super().__init__(
            self,
            symbol_id = f"CONT_FUTURE[{symbol},{exchange},{localSymbol},{multiplier},{currency}]",
            secType="CONTFUT",
            symbol=symbol,
            exchange=exchange,
            localSymbol=localSymbol,
            multiplier=multiplier,
            currency=currency,
            **kwargs)


class Option(Symbol):
    def __init__(
        self,
        symbol: str,
        lastTradeDateOrContractMonth: str,
        strike: float,
        right: str,
        exchange: str,
        multiplier: str,
        currency: str,
        **kwargs):
        """
        Option contract.

        Args:
            symbol: Symbol name.
            lastTradeDateOrContractMonth: The option's last trading day
                or contract month.

                * YYYYMM format: To specify last month
                * YYYYMMDD format: To specify last trading day
            strike: The option's strike price.
            right: Put or call option.
                Valid values are 'P', 'PUT', 'C' or 'CALL'.
            exchange: Destination exchange.
            multiplier: The contract multiplier.
            currency: Underlying currency.
        """
        super().__init__(
            self,
            symbol_id = f"OPTION[{symbol},{lastTradeDateOrContractMonth},{strike},{right},{exchange},{multiplier},{currency}]",
            secType="OPT",
            symbol=symbol,
            lastTradeDateOrContractMonth=lastTradeDateOrContractMonth,
            strike=strike,
            right=right,
            exchange=exchange,
            multiplier=multiplier,
            currency=currency,
            **kwargs)


class FuturesOption(Symbol):
    def __init__(
        self,
        symbol: str,
        lastTradeDateOrContractMonth: str,
        strike: float,
        right: str,
        exchange: str,
        multiplier: str,
        currency: str,
        **kwargs):
        """
        Option on a futures contract.

        Args:
            symbol: Symbol name.
            lastTradeDateOrContractMonth: The option's last trading day
                or contract month.

                * YYYYMM format: To specify last month
                * YYYYMMDD format: To specify last trading day
            strike: The option's strike price.
            right: Put or call option.
                Valid values are 'P', 'PUT', 'C' or 'CALL'.
            exchange: Destination exchange.
            multiplier: The contract multiplier.
            currency: Underlying currency.
        """
        super().__init__(
            self,
            symbol_id = f"FUTURES_OPTION[{symbol},{lastTradeDateOrContractMonth},{strike},{right},{exchange},{multiplier},{currency}]",
            secType="FOP",
            symbol=symbol,
            lastTradeDateOrContractMonth=lastTradeDateOrContractMonth,
            strike=strike,
            right=right,
            exchange=exchange,
            multiplier=multiplier,
            currency=currency,
            **kwargs)


class Index(Symbol):
    def __init__(
        self, 
        symbol: str, 
        exchange: str, 
        currency: str, 
        **kwargs):
        """
        Index.

        Args:
            symbol: Symbol name.
            exchange: Destination exchange.
            currency: Underlying currency.
        """
        super().__init__(
            self, 
            symbol_id = f"INDEX[{symbol},{exchange},{currency}]",
            secType="IND", 
            symbol=symbol, 
            exchange=exchange, 
            currency=currency, 
            **kwargs)


class CFD(Symbol):
    def __init__(
        self, 
        symbol: str, 
        exchange: str, 
        currency: str, 
        **kwargs):
        """
        Contract For Difference.

        Args:
            symbol: Symbol name.
            exchange: Destination exchange.
            currency: Underlying currency.
        """
        super().__init__(
            self, 
            symbol_id = f"CFD[{symbol},{exchange},{currency}]",
            secType="CFD", 
            symbol=symbol, 
            exchange=exchange, 
            currency=currency, 
            **kwargs)


class Commodity(Symbol):
    def __init__(
        self, 
        symbol: str, 
        exchange: str, 
        currency: str, 
        **kwargs):
        """
        Commodity.

        Args:
            symbol: Symbol name.
            exchange: Destination exchange.
            currency: Underlying currency.
        """
        super().__init__(
            self, 
            symbol_id = f"COMMODITY[{symbol},{exchange},{currency}]",
            secType="CMDTY", 
            symbol=symbol, 
            exchange=exchange, 
            currency=currency, 
            **kwargs)


class Bond(Symbol):
    def __init__(
            self,
            symbol: str, 
            exchange: str, 
            currency: str, 
            secIdType: str, 
            secId: str, 
            **kwargs):
        """Bond."""
        super().__init__(
            self, 
            symbol_id = f"BOND[{symbol},{exchange},{currency},{secIdType},{secId}]",
            secType="BOND", 
            symbol=symbol, 
            exchange=exchange, 
            currency=currency, 
            secIdType=secIdType, 
            secId=secId,
            **kwargs)


class MutualFund(Symbol):
    def __init__(
            self,
            symbol: str, 
            exchange: str, 
            currency: str, 
            **kwargs):
        """Mutual fund."""
        super().__init__(
            self, 
            symbol_id = f"MUTUAL_FUND[{symbol},{exchange},{currency}]",
            secType="FUND", 
            symbol=symbol, 
            exchange=exchange, 
            currency=currency,
            **kwargs)


class Warrant(Symbol):
    def __init__(
            self, 
            symbol: str, 
            exchange: str, 
            currency: str, 
            **kwargs):
        """Warrant option."""
        super().__init__(
            self, 
            symbol_id = f"WARRANT[{symbol},{exchange},{currency}]",
            secType="WAR", 
            symbol=symbol, 
            exchange=exchange, 
            currency=currency,
            **kwargs)
        
    
class Crypto(Symbol):
    def __init__(
        self, 
        symbol: str, 
        exchange: str, 
        currency: str, 
        **kwargs):
        """
        Crypto currency contract.

        Args:
            symbol: Symbol name.
            exchange: Destination exchange.
            currency: Underlying currency.
        """
        super().__init__(
            self,
            symbol_id = f"CRYPTO[{symbol},{exchange},{currency}]",
            secType="CRYPTO",
            symbol=symbol,
            exchange=exchange,
            currency=currency,
            **kwargs)
